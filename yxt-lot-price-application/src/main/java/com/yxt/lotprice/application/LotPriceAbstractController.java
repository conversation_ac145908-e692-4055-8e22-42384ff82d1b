package com.yxt.lotprice.application;

import com.yxt.lang.exception.YxtNotLoginException;
import com.yxt.starter.controller.AbstractController;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

public abstract class LotPriceAbstractController extends AbstractController {


    protected final static String HEAD_USER_ID_KEY = "userId";
    protected final static String HEAD_EMP_CODE_KEY = "empcode";
    protected final static String HEAD_USER_ZH_NAME_KEY = "userzhname";
    protected final static String HEAD_USER_NAME_KEY = "username";

    /**
     * @return
     */
    public static String getUserId() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            return request.getHeader(HEAD_USER_ID_KEY);
        }
        return null;
    }
    public static String getUserName() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            return request.getHeader(HEAD_USER_NAME_KEY);
        }
        return null;
    }
    public static String getUserZHName() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            String userZHName = request.getHeader(HEAD_USER_ZH_NAME_KEY);
            if (StringUtils.isBlank(userZHName)){
                return getUserName();
            }else {
                return userZHName;
            }
        }
        return null;
    }

    public static String getEmpCode() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes instanceof ServletRequestAttributes) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            return request.getHeader(HEAD_EMP_CODE_KEY);
        }
        return null;
    }

    /**
     * 获取用户id
     * @return
     */
    public static String getUserIdWithError() {
        String userId = getUserId();
        if (userId == null){
            throw new YxtNotLoginException();
        }
        return userId;
    }
}
