package com.yxt.lotprice.application.b;


import com.yxt.lang.constants.response.ResponseCodeType;
import com.alibaba.fastjson.JSON;
import com.yxt.batch.context.BatchAsyncExportTaskContext;
import com.yxt.batch.service.BatchTaskService;
import com.yxt.common.file.model.ErrorType;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.application.LotPriceAbstractController;
import com.yxt.lotprice.service.PriceAdjustFormService;
import com.yxt.lotprice.service.constant.LocalConstants;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/b/adjust-form")
@Api(tags = "调价单")
@Slf4j
public class AdjustFormController extends LotPriceAbstractController {

    @Resource
    private PriceAdjustFormService priceAdjustFormService;

    private BatchTaskService batchTaskService;

    @ApiOperation(value = "分页查询调价单")
    @PostMapping(value = "/page")
    ResponseBase<PageDTO<AdjustFormPageResp>> page(@RequestBody @Valid AdjustFormPageReq req){
        PageDTO<AdjustFormPageResp> page = priceAdjustFormService.page(req, getUserId());
        return ResponseBase.success(page);
    }

    @ApiOperation(value = "分页查询调价单")
    @PostMapping(value = "/audit")
    ResponseBase<Boolean> audit(@RequestBody @Valid PriceAdjustFormSaveReq req){
        return ResponseBase.success(priceAdjustFormService.audit(req, getUserId()));
    }

    @ApiOperation(value = "新增调价单")
    @PostMapping(value = "/saveAdjustForm")
    ResponseBase<Boolean> saveAdjustForm(@RequestBody @Valid PriceAdjustFormSaveReq req, @RequestHeader("userName") String userName,@RequestHeader("userId") String userId){
        return ResponseBase.success(priceAdjustFormService.savePriceAdjustForm(req, userName,userId));
    }

    @ApiOperation(value = "调价单整单作废")
    @PostMapping(value = "/invalidAdjustForm")
    ResponseBase<Boolean> invalidAdjustForm(@RequestBody InvalidAdjustFormReq req, @RequestHeader("userName") String userName, @RequestHeader("userId") String userId){
        return ResponseBase.success(priceAdjustFormService.invalidPriceAdjustForm(req, userName,userId));
    }

    @ApiOperation(value = "调价单整明细作废")
    @PostMapping(value = "/invalidAdjustFormItem")
    ResponseBase<Boolean> invalidAdjustFormItem(@RequestBody InvalidAdjustFormReq req, @RequestHeader("userName") String userName, @RequestHeader("userId") String userId){
        if (CollectionUtils.isEmpty(req.getItemIds())){
            return ResponseBase.fail(ResponseCodeType.PARA_ERROR, "请选择要作废的明细");
        }
        //todo 校验是否有权限作废
        return ResponseBase.success(priceAdjustFormService.invalidPriceAdjustFormItem(req, userName,userId));
    }

    @ApiOperation(value = "调价单详情")
    @GetMapping(value = "/getAdjustFormInfo/{code}")
    ResponseBase<AdjustFormInfoResp> adjustFormInfo(@PathVariable String code, @RequestHeader("userName") String userName, @RequestHeader("userId") String userId){
        return ResponseBase.success(priceAdjustFormService.getAdjustFormInfo(code, userName,userId));
    }



    @PostMapping("/importCompanyPrice")
    @ApiOperation(value = "导入公司调价", notes = "导入公司调价")
    public ResponseBase<Void> importCompanyPrice(@RequestParam("file") MultipartFile multipartFile,@RequestHeader("userName") String userName,@RequestHeader("userId") String userId) {
        priceAdjustFormService.importCompanyPrice(multipartFile,userName,userId);
        return ResponseBase.success();
    }
    @PostMapping("/importGroupPrice")
    @ApiOperation(value = "导入价格组调价", notes = "导入价格组调价")
    public ResponseBase<Void> importGroupPrice(@RequestParam("file") MultipartFile multipartFile,@RequestHeader("userName") String userName,@RequestHeader("userId") String userId) {
        priceAdjustFormService.importGroupPrice(multipartFile,userName,userId);
        return ResponseBase.success();
    }
    @PostMapping("/importStorePrice")
    @ApiOperation(value = "导入门店调价", notes = "导入门店调价")
    public ResponseBase<Void> importStorePrice(@RequestParam("file") MultipartFile multipartFile,@RequestHeader("userName") String userName,@RequestHeader("userId") String userId) {
        priceAdjustFormService.importStorePrice(multipartFile,userName,userId);
        return ResponseBase.success();
    }

    @ApiOperation(value = "调价单复制")
    @PostMapping(value = "/copyAdjustForm")
    ResponseBase<CopyAdjustFormResp> copyAdjustForm(@RequestBody CopyAdjustFormReq req, @RequestHeader("userId") String userId){
        return ResponseBase.success(priceAdjustFormService.copyAdjustForm(req, userId));
    }

    @ApiOperation(value = "获取当前登录人条件范围权限")
    @PostMapping(value = "/currentUserAdjustScope")
    ResponseBase<CurrentUserAdjustScopeResp> currentUserAdjustScope(@RequestHeader("userId") String userId){
        return ResponseBase.success(priceAdjustFormService.getCurrentUserAdjustScope(userId));
    }

    @ApiOperation(value = "当前用户可选择的公司")
    @PostMapping(value = "/currentUserCompany")
    ResponseBase<List<CurrentUserCompanyResp>> currentUserCompany(@RequestHeader("userId") String userId){
        return ResponseBase.success(priceAdjustFormService.currentUserCompany(userId));
    }

    @ApiOperation(value = "调价单审批记录")
    @PostMapping(value = "/adjustFormAporovalLog")
    ResponseBase<List<AdjustFormAporovalLogResp>> adjustFormAporovalLog(@RequestBody AdjustFormAporovalLogReq req){
        return ResponseBase.success(priceAdjustFormService.getAdjustFormAporovalLog(req));
    }

    @ApiOperation(value = "查询枚举值，调价单相关")
    @PostMapping(value = "/adjustFormEnum")
    ResponseBase<List<SelectorDTO>> adjustFormEnum(@RequestBody AdjustFormEnumReq req){

        return ResponseBase.success(priceAdjustFormService.getAdjustFormEnum(req));
    }

    @ApiOperation(value = "单店导出", notes = "单店导出")
    @PostMapping(value = "/export")
    public ResponseBase exportCommodityInfo(@Valid @RequestBody PriceAdjustFormExportReq adjustReq, BindingResult result,
                                            @RequestHeader(value = "userName") String userName) {
        checkValid(result);
        try {
            Map<String, Object> paramMap = JSON.parseObject(JSON.toJSONString(adjustReq), Map.class);
            BatchAsyncExportTaskContext context = BatchAsyncExportTaskContext.builder()
                    .merCode(LocalConstants.MER_CODE_500001)
                    .exportModel(PriceAdjustFormExportResp.class)
                    .exportSearchCondition(paramMap)
                    .userName(userName)
                    .build();
            batchTaskService.submitAsyncExportBatchTask(context);
            return ResponseBase.success();
        } catch (Exception e) {
            ExLogger.logger().warn("导出门店调价数据失败", e);
        }
        return ResponseBase.fail(ErrorType.OPERATOR_ERROR.getCode(), ErrorType.OPERATOR_ERROR.getMsg());
    }


    @ApiOperation(value = "单店审核导入")
    @PostMapping(value = "/importSingleStoreAudit")
    ResponseBase<Void> importSingleStoreAudit(@RequestParam("file") MultipartFile file,@RequestHeader("userId") String userId,@RequestHeader("userName") String userName){
        priceAdjustFormService.importSingleStoreAudit(file,userId,userName);
        return ResponseBase.success();
    }
}
