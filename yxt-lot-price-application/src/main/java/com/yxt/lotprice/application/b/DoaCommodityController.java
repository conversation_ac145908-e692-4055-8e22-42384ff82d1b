package com.yxt.lotprice.application.b;


import com.yxt.lang.constants.response.ResponseCodeType;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.application.LotPriceAbstractController;
import com.yxt.lotprice.service.DoaCommodityService;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommodityRemoveReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommoditySaveReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.request.adjustbase.DoaCommoditySearchReq;
import com.yxt.lotprice.service.adjustbase.model.dto.b.response.adjustbase.DoaCommoditySearchResp;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping(value = "/b/doa")
@Api(tags = "权限下放商品")
@Slf4j
public class DoaCommodityController extends LotPriceAbstractController {

    @Autowired
    DoaCommodityService doaCommodityService;

    @ApiOperation(value = "新增下放清单")
    @PostMapping(value = "/save")
    ResponseBase<String> save(@RequestBody @Valid DoaCommoditySaveReq req) {
        return ResponseBase.success(doaCommodityService.save(req, getUserZHName()));
    }

    @ApiOperation(value = "查询下放清单")
    @PostMapping(value = "/page")
    ResponseBase<PageDTO<DoaCommoditySearchResp>> page(@RequestBody @Valid DoaCommoditySearchReq req) {
        if (CollectionUtils.isEmpty(req.getErpCodes()) && CollectionUtils.isEmpty(req.getOrgs())){
            return ResponseBase.fail(ResponseCodeType.PARA_ERROR.getCode(), "erp编码和部门编码不能同时为空");
        }
        PageDTO<DoaCommoditySearchResp> pages = doaCommodityService.page(req);
        return ResponseBase.success(pages);
    }

    @ApiOperation(value = "移除下放清单明细")
    @PostMapping(value = "/remove")
    ResponseBase<Long> remove(@RequestBody @Valid DoaCommodityRemoveReq req) {
        if (CollectionUtils.isEmpty(req.getErpCodes()) && CollectionUtils.isEmpty(req.getOrgs()) && CollectionUtils.isEmpty(req.getIds()) && StringUtils.isEmpty(req.getCommodityName())){
            return ResponseBase.fail(ResponseCodeType.PARA_ERROR.getCode(), "erp编码和部门编码和下放商品清单id不能同时为空");
        }
        Long count = doaCommodityService.remove(req);
        return ResponseBase.success(count);
    }
}
