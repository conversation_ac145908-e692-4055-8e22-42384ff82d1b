package com.yxt.lotprice.application.b;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.lotprice.service.PriceAdjustRoleConfigService;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import com.yxt.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 10:05
 */
@RestController
@RequestMapping(value = "/b/priceAdjustRoleConfig")
@Api(tags = "调价范围配置")
@Slf4j
public class PriceAdjustRoleConfigController extends AbstractController {
    @Resource
    private PriceAdjustRoleConfigService priceAdjustRoleConfigService;

    @ApiOperation(value = "调价范围列表查询")
    @PostMapping(value = "/priceAdjustRangePage")
    ResponseBase<PageDTO<PriceAdjustRangePageResp>> priceAdjustRangePage(@RequestBody PriceAdjustRangePageReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.priceAdjustRangePage(req));
    }
    @ApiOperation(value = "报批人配置列表")
    @PostMapping(value = "/submitterPage")
    ResponseBase<PageDTO<SubmitterPageResp>> submitterPage(@RequestBody SubmitterPageReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.submitterPage(req));
    }
    @ApiOperation(value = "新增编辑报批人")
    @PostMapping(value = "/addOrUpdateSubmitter")
    ResponseBase<Boolean> addOrUpdateSubmitter(@RequestBody AddOrEditSubmitterReq req, @RequestHeader("userName") String userName){
        return ResponseBase.success(priceAdjustRoleConfigService.addSubmitter(req,userName));
    }

    @ApiOperation(value = "校验报批人组织")
    @PostMapping(value = "/checkSubmitter")
    ResponseBase<CheckSubmitterResp> checkSubmitter(@RequestBody AddOrEditSubmitterReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.checkSubmitter(req));
    }

    @ApiOperation(value = "新增编辑审批人")
    @PostMapping(value = "/addOrUpdateApprover")
    ResponseBase<Boolean> addOrUpdateApprover(@RequestBody AddOrEditApproverReq req, @RequestHeader("userName") String userName){
        return ResponseBase.success(priceAdjustRoleConfigService.addApprover(req,userName));
    }

    @ApiOperation(value = "审批人配置列表")
    @PostMapping(value = "/approverPage")
    ResponseBase<PageDTO<ApproverPageResp>> approverPage(@RequestBody ApproverPageReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.approverPage(req));
    }

    @ApiOperation(value = "审批人配置详情")
    @GetMapping(value = "/approverDetail/{code}")
    ResponseBase<ApproverDetailResp> approverDetail(@PathVariable("code") String code){
        return ResponseBase.success(priceAdjustRoleConfigService.approverDetail(code));
    }

    @ApiOperation(value = "报批人配置详情")
    @GetMapping(value = "/submitterDetail/{code}")
    ResponseBase<SubmitterDetailResp> submitterDetail(@PathVariable("code") String code){
        return ResponseBase.success(priceAdjustRoleConfigService.submitterDetail(code));
    }

    @ApiOperation(value = "报批人类别下拉框")
    @PostMapping(value = "/roleSelector")
    ResponseBase<List<SelectorDTO>> roleSelector(@RequestBody RoleSelectorReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.roleSelector(req));
    }

    @ApiOperation(value = "价格类型下拉框")
    @PostMapping(value = "/priceTypeSelector")
    ResponseBase<List<SelectorDTO>> priceTypeSelector(){
        return ResponseBase.success(priceAdjustRoleConfigService.priceTypeSelector());
    }

    @ApiOperation(value = "所属子公司下拉框")
    @PostMapping(value = "/companySelector")
    ResponseBase<List<SelectorDTO>> companySelector(){
        return ResponseBase.success(priceAdjustRoleConfigService.companySelector());
    }

    @ApiOperation(value = "按公司/集团+角色信息定位到具体的调价范围列表")
    @PostMapping(value = "/locateRoleConfig")
    ResponseBase<LocateRoleConfigResp> locateRoleConfig(@RequestBody @Valid LocateRoleConfigReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.locateRoleConfig(req));
    }

    @ApiOperation(value = "去设置页面")
    @PostMapping(value = "/queryToEditPage")
    ResponseBase<PageDTO<ToEditPageResp>> queryToEditPage(@RequestBody ToEditPageReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.queryToEditPage(req));
    }

    @ApiOperation(value = "定/调价维度下拉框")
    @PostMapping(value = "/dimensionSelector")
    ResponseBase<List<SelectorDTO>> dimensionSelector(){
        return ResponseBase.success(priceAdjustRoleConfigService.dimensionSelector());
    }

    @ApiOperation(value = "员工分页查询")
    @PostMapping(value = "/userPage")
    ResponseBase<PageDTO<UserDTO>> userPage(@RequestBody UserPageReq req){
        return ResponseBase.success(priceAdjustRoleConfigService.userPage(req));
    }
}
