package com.yxt.lotprice.application.mq.rocketmq.consumer;

import com.alibaba.fastjson.JSON;
import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.common.model.constant.MqConsumerGroupConstant;
import com.yxt.lotprice.common.model.constant.MqTopicConstant;
import com.yxt.lotprice.service.PriceAdjustFormItemService;
import com.yxt.lotprice.service.calculation.PriceAdjustCalculationService;
import com.yxt.lotprice.service.calculation.PriceAdjustFormItemDetailService;
import com.yxt.lotprice.service.calculation.PriceAdjustResultsService;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.bo.PriceResultsBO;
import com.yxt.lotprice.service.model.dto.mq.rocketmq.StoreChangeDTO;
import com.yxt.lotprice.service.model.enums.MqStoreChangeTypeEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.log.PriceCalculateSourceEnum;
import com.yxt.lotprice.service.mq.PricePublishService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 门店新增 价格组/分公司变化处理重新计算价格逻辑消费者
 * Since: 2025/05/28 14:24
 * Author: qs
 */

@Component
@RocketMQMessageListener(topic = MqTopicConstant.TP_STORE_INFO_UPDATE
        , consumerGroup = MqConsumerGroupConstant.CG_STORE_INFO_CHANGE_CALCULATION_PRICE, consumeThreadMax = 2)
public class StoreInfoChangeReCalculationPriceConsumer implements RocketMQListener<MessageExt> {

    @Resource
    private PriceAdjustFormItemService priceAdjustFormItemService;
    @Resource
    private PriceAdjustFormItemDetailService priceAdjustFormItemDetailService;
    @Resource
    private PriceAdjustCalculationService priceAdjustCalculationService;
    @Resource
    private PriceAdjustResultsService priceAdjustResultsService;
    @Resource
    private PricePublishService pricePublishService;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody());
        ExLogger.logger(MqConsumerGroupConstant.CG_STORE_INFO_CHANGE_CALCULATION_PRICE).info("收到门店变动消息 msgId:{}, body:{}", message.getMsgId(), body);
        StoreChangeDTO storeChangeDTO = JSON.parseObject(body, StoreChangeDTO.class);
        if (StringUtils.isEmpty(storeChangeDTO.getStCode())) {
            ExLogger.logger().error("门店编码为空 msg: {}", message);
            return;
        }
        if (MqStoreChangeTypeEnum.STORE_ADD.getValue().equals(storeChangeDTO.getOperateType())) {
            // 新增逻辑 查询门店所有的调价单 -> 生成调价单价格明细 -> 计算结果 -> 下发价格
            // 查询调价单：门店编码、价格组、分公司

            // 生成价格明细，计算价格
            for (PriceTypeEnum value : PriceTypeEnum.values()) {
                calculationPriceByStore(storeChangeDTO, value);
            }
        } else if (CollectionUtils.isNotEmpty(storeChangeDTO.getData())) {
            // 修改逻辑 只处理修改价格组、分公司逻辑
            storeChangeDTO.getData().forEach(data -> {
                if (MqStoreChangeTypeEnum.PRICE_GROUP_MODIFY.name().equals(data.getChangeType())
                        || MqStoreChangeTypeEnum.BRANCH_CODE_MODIFY.name().equals(data.getChangeType())) {
                    // 删除原价格明细，生成新价格明细，计算价格
                    for (PriceTypeEnum priceTypeValue : PriceTypeEnum.values()) {
                        deleteCalculationPriceByStoreOrgCode(data, priceTypeValue, storeChangeDTO);
                    }

                }
            });
        }

    }

    // 删除原来的数据，插入新的数据 计算价格下发
    private void deleteCalculationPriceByStoreOrgCode(StoreChangeDTO.StoreChangeDetailDTO data, PriceTypeEnum priceTypeValue, StoreChangeDTO storeChangeDTO) {
        // 查询该价格组/分公司的所有调价单
        List<PriceAdjustFormItemBO> itemBOList = priceAdjustFormItemService.listEnableByPriceTypeOrgCode(data.getNewValue(), priceTypeValue);
        Set<String> addDetailErpCodeSet = itemBOList.stream().map(PriceAdjustFormItemBO::getErpCode).collect(Collectors.toSet());
        // 写入明细
        priceAdjustFormItemDetailService.saveFormItemDetailSingleStore(storeChangeDTO.getStCode(), itemBOList);
        // 删除计算明细 删除计算在同一事务
        // 查询需要删除的价格明细
        List<PriceAdjustFormItemDetailBO> deleteDetailList = priceAdjustFormItemDetailService.listByStoreAndOrgCode(storeChangeDTO.getStCode(), data.getOldValue(), priceTypeValue);

        // 数量小一点控制事务执行时间
        ListUtils.partition(deleteDetailList, 10).forEach(deleteDetail -> {
            // 删除价门店属于价格组/分公司的调价单明细 并重新计算价格明细
            transactionTemplate.execute(transactionStatus -> {
                // 删除数据
                priceAdjustFormItemDetailService.deleteByStoreAndIds(storeChangeDTO.getStCode(), deleteDetail.stream().map(PriceAdjustFormItemDetailBO::getId).collect(Collectors.toList()));
                Set<String> deleteErpCodeSet = deleteDetail.stream().map(PriceAdjustFormItemDetailBO::getErpCode).collect(Collectors.toSet());
                // 计算保存下发价格 todo qs 可以考虑不查询一部分明细
                priceAdjustCalculationService.calculateSavePublish(priceTypeValue, storeChangeDTO.getStCode(), new ArrayList<>(deleteErpCodeSet));
                // 从新增重新计算价格的商品中移除，避免重复计算
                addDetailErpCodeSet.removeAll(deleteErpCodeSet);
                return null;
            });
        });

        // 重新计算新增商品的价格
        if (CollectionUtils.isNotEmpty(addDetailErpCodeSet)) {
            // 计算保存下发价格 todo qs 可以考虑不查询一部分明细
            priceAdjustCalculationService.calculateSavePublish(priceTypeValue, storeChangeDTO.getStCode(), new ArrayList<>(addDetailErpCodeSet));
        }
    }

    // 计算一个门店一种类型价格
    private void calculationPriceByStore(StoreChangeDTO storeChangeDTO, PriceTypeEnum priceTypeEnum) {
        // todo qs 保存调价单明细数据到price_adjust_form_item_detail表 非门店维度需要查询门店商品经营范围做过滤
        List<PriceAdjustFormItemBO> retailPriceItemList = priceAdjustFormItemService.listAllEnableByStoreCodeAndType(storeChangeDTO.getStCode(), priceTypeEnum);
        priceAdjustFormItemDetailService.saveFormItemDetailSingleStore(storeChangeDTO.getStCode(), retailPriceItemList);

        List<PriceAdjustFormItemDetailBO> priceDetailList = new ArrayList<>();
        List<PriceResultsBO> priceResultsBOList = priceAdjustCalculationService.calculatePriceResult(priceDetailList);
        // 保存结果 分批执行
        List<List<PriceResultsBO>> partition = ListUtils.partition(priceResultsBOList, 1000);
        partition.forEach(newList -> {
            List<PriceResultsBO> oldList = priceAdjustResultsService.listByStoreCodePriceTypeErpCodes(storeChangeDTO.getStCode(), priceTypeEnum, newList.stream().map(PriceResultsBO::getErpCode).collect(Collectors.toSet()));
            List<PriceResultsBO> publishList = priceAdjustCalculationService.saveOrUpdateAndGetPublishPrice(PriceCalculateSourceEnum.STORE_ADD, newList, oldList);
            pricePublishService.publishPrice(pricePublishService.buildPublishData(publishList));
        });
    }
}
