package com.yxt.lotprice;

import cn.hutool.crypto.GlobalBouncyCastleProvider;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 会员慢病应用
 * <AUTHOR>
 */

@MapperScan(basePackages = {"com.yxt.**.infrastructure.dao.mapper","com.yxt.batch.**", "com.yxt.common.**"})
@EnableFeignClients(basePackages = "com.yxt.**")
@EnableDiscoveryClient
@EnableApolloConfig
@ServletComponentScan(basePackages = {"com.yxt.**.infrastructure.config"})
@SpringBootApplication(exclude= {DataSourceAutoConfiguration.class}, scanBasePackages = {"com.yxt.lotprice.**","com.yxt.batch.**", "com.yxt.common.**"})
@EnableAsync
public class Application {

    public static void main(String[] args) {
//        GlobalBouncyCastleProvider.setUseBouncyCastle(false);
        SpringApplication.run(Application.class, args);
        System.out.println("Started");
    }

}
