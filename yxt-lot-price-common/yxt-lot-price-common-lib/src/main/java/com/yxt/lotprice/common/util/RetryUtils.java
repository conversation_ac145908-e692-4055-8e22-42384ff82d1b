package com.yxt.lotprice.common.util;

import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

/**
 * @Author: mengzilei
 * @Date: 2023-12-24  12:12
 */
@Slf4j
public class RetryUtils {

    /**
     * 返回的结果必须是list
     *
     * @param retryTimes
     * @param supplier
     * @param <T>
     * @return
     */
    public static <T> T retry(int retryTimes, Supplier<T> supplier){
        long start = 0L;
        long duration = 0L;
        T t = null;
        for (int i = 0; i <= retryTimes; i++) {
            try {
                start = System.currentTimeMillis();
                t = supplier.get();
                return t;
            } catch (Exception e) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
                if (retryTimes == i) {
                    duration = System.currentTimeMillis() - start;
                    log.info("重试失败,重试次数：" + retryTimes+"耗时："+duration,e);
                    throw e;//重试无效则抛出异常
                }
            }

        }
        return t;
    }
    /**
     * 返回的结果必须是list
     *
     * @param retryTimes
     * @param supplier
     * @return
     */
    public static <T> void retryReVoid(int retryTimes, int sleep, Supplier<T> supplier) {
        long start=0L;
        long duration = 0L;
        for (int i = 0; i <= retryTimes; i++) {
            try {
                start = System.currentTimeMillis();
                supplier.get();
                return;
            } catch (Exception e) {
                if (retryTimes == i) {
                    duration = System.currentTimeMillis() - start;
                    log.error(supplier.toString()+"重试次数：" + retryTimes+"耗时："+duration,e);
                }
                try {
                    Thread.sleep(sleep);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }
    }
}
