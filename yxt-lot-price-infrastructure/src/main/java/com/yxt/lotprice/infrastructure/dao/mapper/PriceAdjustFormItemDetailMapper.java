package com.yxt.lotprice.infrastructure.dao.mapper;

import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormItemDetailPO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * <p>
 * Mapper接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
public interface PriceAdjustFormItemDetailMapper extends BaseMapper<PriceAdjustFormItemDetailPO> {
    /**
     * 批量插入
     */
    boolean insertBatch(@Param("records") List<PriceAdjustFormItemDetailPO> records);

    /**
     * 批量插入更新（ON DUPLICATE KEY UPDATE）
     */
    boolean insertBatchOnDuplicateKeyUpdate(@Param("records") List<PriceAdjustFormItemDetailPO> records);
}