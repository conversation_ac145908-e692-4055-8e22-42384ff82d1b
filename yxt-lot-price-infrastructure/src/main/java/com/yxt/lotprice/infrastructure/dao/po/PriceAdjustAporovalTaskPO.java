package com.yxt.lotprice.infrastructure.dao.po;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * <p>
 * 调价申请单审批表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
@TableName("price_adjust_aporoval_task")
public class PriceAdjustAporovalTaskPO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 审批流id（编码相同，版本不同）
     */
    private Long priceAdjustFormId;

    /**
     * 调价单状态，审核中，已审核，草稿，已驳回，已撤销，待审核
     */
    private String status;

    /**
     * 审核进度
     */
    private String aporovalProgress;

    /**
     * 定价/调价维度，STORE-门店,PRICE_GROUP-价格组,COMPANY-子公司
     */
    private String dimension;

    /**
     * 作废状态:未作废，部分作废，已作废
     */
    private String cancelStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 备注
     */
    private String descs;

    /**
     * 定价/调价类别：定价，调价
     */
    private String priceOpsType;

    /**
     * 调价单id
     */
    private Long flowDefinitionId;

}
