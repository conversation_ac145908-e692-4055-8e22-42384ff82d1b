package com.yxt.lotprice.manager.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustAporovalTaskMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustAporovalTaskPO;
import com.yxt.lotprice.service.manager.iface.ApprovalFlowTaskManager;
import com.yxt.lotprice.service.model.bo.PriceAdjustAporovalTaskBO;
import com.yxt.lotprice.service.model.dto.b.response.AdjustFormAporovalTaskResp;
import javafx.util.BuilderFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Service
public class ApprovalFlowTaskManagerImpl implements ApprovalFlowTaskManager {

    @Resource
    private PriceAdjustAporovalTaskMapper priceAdjustAporovalTaskMapper;

    @Override
    public List<PriceAdjustAporovalTaskBO> getApprovalTaskByFormId(Long formId, Long flowDefinitionId) {
        if (formId == null || flowDefinitionId == null) {
            return null;
        }
        LambdaQueryWrapper<PriceAdjustAporovalTaskPO> wrapper = new LambdaQueryWrapper<PriceAdjustAporovalTaskPO>()
                .eq(PriceAdjustAporovalTaskPO::getPriceAdjustFormId, formId)
                .eq(PriceAdjustAporovalTaskPO::getFlowDefinitionId, flowDefinitionId);
        wrapper.orderByAsc(PriceAdjustAporovalTaskPO::getId);
        List<PriceAdjustAporovalTaskPO> priceAdjustAporovalTaskPOS = priceAdjustAporovalTaskMapper.selectList(wrapper);
        if(CollUtil.isEmpty(priceAdjustAporovalTaskPOS)){
            return null;
        }
        List<PriceAdjustAporovalTaskBO> list = new ArrayList<>();
        for (PriceAdjustAporovalTaskPO priceAdjustAporovalTaskPO : priceAdjustAporovalTaskPOS) {
            PriceAdjustAporovalTaskBO resp = new PriceAdjustAporovalTaskBO();
            BeanUtils.copyProperties(priceAdjustAporovalTaskPO, resp);
            list.add(resp);
        }
        return list;
    }

    @Override
    public void createTask(PriceAdjustAporovalTaskBO taskBO) {
        priceAdjustAporovalTaskMapper.insert(BeanUtil.copyProperties(taskBO, PriceAdjustAporovalTaskPO.class));
    }
}
