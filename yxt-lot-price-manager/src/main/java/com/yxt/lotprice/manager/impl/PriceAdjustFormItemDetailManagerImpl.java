package com.yxt.lotprice.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormItemDetailMapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormItemDetailPO;
import com.yxt.lotprice.service.cache.AllStoreInfoCache;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormItemDetailManager;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/05/22 9:52
 * Author: qs
 */
@Service
public class PriceAdjustFormItemDetailManagerImpl implements PriceAdjustFormItemDetailManager {

    @Resource
    private PriceAdjustFormItemDetailMapper priceAdjustFormItemDetailMapper;

    @Override
    public List<PriceAdjustFormItemDetailBO> listEnableByStoreCodeErpCodes(PriceTypeEnum priceTypeEnum, String companyCode, String storeCode, Collection<String> erpCodes) {
        List<PriceAdjustFormItemDetailPO> priceAdjustFormItemDetailPOS = priceAdjustFormItemDetailMapper.selectList(new LambdaQueryWrapper<PriceAdjustFormItemDetailPO>()
                .eq(PriceAdjustFormItemDetailPO::getCompanyCode, companyCode)
                .eq(PriceAdjustFormItemDetailPO::getStoreCode, storeCode)
                .in(PriceAdjustFormItemDetailPO::getErpCode, erpCodes)
                .eq(priceTypeEnum != null, PriceAdjustFormItemDetailPO::getPriceType, priceTypeEnum)
                .eq(PriceAdjustFormItemDetailPO::getEnableStatus, CancelStatusEnum.NOT_CANCELED)
                .ge(PriceAdjustFormItemDetailPO::getEndTime, LocalDate.now())
        );

        return BeanUtil.copyToList(priceAdjustFormItemDetailPOS, PriceAdjustFormItemDetailBO.class);
    }

    @Override
    public List<PriceAdjustFormItemDetailBO> listByStoreAndOrgCode(String companyCode, String storeCode, String priceGroupCode, PriceTypeEnum priceTypeEnum) {
        List<PriceAdjustFormItemDetailPO> priceAdjustFormItemDetailPOS = priceAdjustFormItemDetailMapper.selectList(new LambdaQueryWrapper<PriceAdjustFormItemDetailPO>()
                .eq(PriceAdjustFormItemDetailPO::getCompanyCode, companyCode)
                .eq(PriceAdjustFormItemDetailPO::getStoreCode, storeCode)
                .eq(PriceAdjustFormItemDetailPO::getOrgCode, priceGroupCode)
                .eq(priceTypeEnum != null, PriceAdjustFormItemDetailPO::getPriceType, priceTypeEnum)
        );

        return BeanUtil.copyToList(priceAdjustFormItemDetailPOS, PriceAdjustFormItemDetailBO.class);
    }

    @Override
    public boolean insertBatch(Collection<PriceAdjustFormItemDetailBO> records) {
        return priceAdjustFormItemDetailMapper.insertBatch(BeanUtil.copyToList(records, PriceAdjustFormItemDetailPO.class));
    }

    @Override
    public void insertBatchOnDuplicateKeyUpdate(Collection<PriceAdjustFormItemDetailBO> records) {
        priceAdjustFormItemDetailMapper.insertBatchOnDuplicateKeyUpdate(BeanUtil.copyToList(records, PriceAdjustFormItemDetailPO.class));
    }

    @Override
    public int deleteByStoreAndIds(String companyCode, String storeCode, Collection<Long> ids) {
        return priceAdjustFormItemDetailMapper.delete(new LambdaQueryWrapper<PriceAdjustFormItemDetailPO>()
                .eq(PriceAdjustFormItemDetailPO::getCompanyCode, companyCode)
                .eq(PriceAdjustFormItemDetailPO::getStoreCode, storeCode)
                .in(PriceAdjustFormItemDetailPO::getId, ids)
        );
    }
}
