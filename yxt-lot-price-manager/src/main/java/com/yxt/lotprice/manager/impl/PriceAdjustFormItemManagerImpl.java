package com.yxt.lotprice.manager.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.common.util.MiddleIdClient;
import com.yxt.lotprice.common.util.RetryUtils;
import com.yxt.lotprice.infrastructure.dao.mapper.PriceAdjustFormItem1Mapper;
import com.yxt.lotprice.infrastructure.dao.po.PriceAdjustFormItemPO;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormItemManager;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemSearchBO;
import com.yxt.lotprice.service.model.bo.SearchAfterBO;
import com.yxt.lotprice.service.model.dto.b.request.AdjustFormAporovalLogReq;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustFormItemDTO;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustFormSaveReq;
import com.yxt.lotprice.service.model.dto.b.response.AdjustFormAporovalLogResp;
import com.yxt.lotprice.service.model.dto.b.response.PriceAdjustFormItemResp;
import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.model.enums.ItemExecStatusEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/26 16:02
 */
@Service
@Slf4j
public class PriceAdjustFormItemManagerImpl implements PriceAdjustFormItemManager {

    @Resource
    private PriceAdjustFormItem1Mapper priceAdjustFormItem1Mapper;

    @Resource
    private MiddleIdClient middleIdClient;

    @Override
    public void savePriceAdjustFormItem(PriceAdjustFormSaveReq req, String userName) {

        List<PriceAdjustFormItemDTO> items = req.getItems();

        List<PriceAdjustFormItemPO> poList = new ArrayList<>();
        for (PriceAdjustFormItemDTO item : items) {
            //零售价
            if (item.getRetailPrice() != null){
                PriceAdjustFormItemPO retailPricePo = new PriceAdjustFormItemPO();
                fillParameters(req, userName, item, retailPricePo);
                retailPricePo.setRequestPriceType(PriceTypeEnum.RETAIL.name());
                retailPricePo.setRequestPrice(item.getRetailPrice());
                retailPricePo.setRequestStartTime(item.getRetailPriceBeginTime());
                retailPricePo.setRequestEndTime(item.getRetailPriceEndTime());
                poList.add(retailPricePo);
            }
            //会员价
            if (item.getVipPrice() != null){
                PriceAdjustFormItemPO vipPricePo = new PriceAdjustFormItemPO();
                fillParameters(req, userName, item, vipPricePo);
                vipPricePo.setRequestPrice(item.getVipPrice());
                vipPricePo.setRequestStartTime(item.getVipPriceBeginTime());
                vipPricePo.setRequestEndTime(item.getVipPriceEndTime());
                vipPricePo.setRequestPriceType(PriceTypeEnum.VIP.name());
                poList.add(vipPricePo);
            }
            //慢病价
            if (item.getVipPrice() != null){
                PriceAdjustFormItemPO chronicPricePo = new PriceAdjustFormItemPO();
                fillParameters(req, userName, item, chronicPricePo);
                chronicPricePo.setRequestPrice(item.getChronicPrice());
                chronicPricePo.setRequestStartTime(item.getChronicPriceBeginTime());
                chronicPricePo.setRequestEndTime(item.getChronicPriceEndTime());
                chronicPricePo.setRequestPriceType(PriceTypeEnum.CHRONIC.name());
                poList.add(chronicPricePo);
            }
        }
        priceAdjustFormItem1Mapper.insertBatch(poList);
    }

    private void fillParameters(PriceAdjustFormSaveReq req, String userName, PriceAdjustFormItemDTO item, PriceAdjustFormItemPO retailPricePo) {
        retailPricePo.setId(middleIdClient.getId(1).get(0));
        retailPricePo.setParentCode(req.getCode());
        retailPricePo.setOrgType(req.getDimension().name());
        retailPricePo.setOrgCode(item.getOrgCode());
        retailPricePo.setOrgName(item.getOrgName());
        retailPricePo.setStatus(CancelStatusEnum.NOT_CANCELED.name());
        retailPricePo.setCreateName(userName);
        retailPricePo.setModifyName(userName);
        retailPricePo.setCrateTime(LocalDateTime.now());
        retailPricePo.setModifyTime(LocalDateTime.now());
        retailPricePo.setExecStatus(ItemExecStatusEnum.NOT_STARTED.name());
        retailPricePo.setScope(req.getScope().name());
        retailPricePo.setErpCode(item.getErpCode());
        retailPricePo.setCompetitorName(item.getCompetitorName());
        retailPricePo.setCompetitorPrice(item.getCompetitorPrice());
        retailPricePo.setCompetitorImg(item.getCompetitorImg());
    }

    @Override
    public void updatePriceAdjustFormItem(PriceAdjustFormSaveReq req, String userName) {
        LambdaQueryWrapper<PriceAdjustFormItemPO> wrapper = new LambdaQueryWrapper<PriceAdjustFormItemPO>().eq(PriceAdjustFormItemPO::getParentCode, req.getCode());
        priceAdjustFormItem1Mapper.delete(wrapper);

        savePriceAdjustFormItem(req, userName);
    }

    @Override
    public List<PriceAdjustFormItemResp> getPriceAdjustFormItem(String code) {
        LambdaQueryWrapper<PriceAdjustFormItemPO> wrapper = new LambdaQueryWrapper<PriceAdjustFormItemPO>().eq(PriceAdjustFormItemPO::getParentCode, code);
        List<PriceAdjustFormItemPO> poList = priceAdjustFormItem1Mapper.selectList(wrapper);
        List<String> erpCodeList = poList.stream().map(PriceAdjustFormItemPO::getErpCode).collect(Collectors.toList());
        return Collections.emptyList();
    }

    @Override
    public List<PriceAdjustFormItemBO> listEnableByPriceTypeOrgCode(String orgCode, PriceTypeEnum priceTypeEnum) {
        List<PriceAdjustFormItemPO> formItemList = priceAdjustFormItem1Mapper.selectList(new LambdaQueryWrapper<PriceAdjustFormItemPO>()
                .eq(PriceAdjustFormItemPO::getOrgCode, orgCode)
                .eq(PriceAdjustFormItemPO::getRequestPriceType, priceTypeEnum.name())
                .eq(PriceAdjustFormItemPO::getIsValid, 1)
                .ge(PriceAdjustFormItemPO::getAporovalEndTime, LocalDate.now())
        );
        return BeanUtil.copyToList(formItemList, PriceAdjustFormItemBO.class);
    }

    @Override
    public List<PriceAdjustFormItemBO> listByCodeAndErpCodes(String code, List<String> erpCodeList) {
        List<PriceAdjustFormItemPO> formItemList = priceAdjustFormItem1Mapper.selectList(new LambdaQueryWrapper<PriceAdjustFormItemPO>()
                .eq(PriceAdjustFormItemPO::getParentCode, code)
                .in(PriceAdjustFormItemPO::getErpCode, erpCodeList));
        return BeanUtil.copyToList(formItemList, PriceAdjustFormItemBO.class);
    }

    @Override
    public List<AdjustFormAporovalLogResp> getAdjustFormAporovalLog(AdjustFormAporovalLogReq req) {
        LambdaQueryWrapper<PriceAdjustFormItemPO> wrapper = new LambdaQueryWrapper<PriceAdjustFormItemPO>()
                .eq(PriceAdjustFormItemPO::getParentCode, req.getCode())
                .eq(PriceAdjustFormItemPO::getOrgCode, req.getOrCode())
                .eq(PriceAdjustFormItemPO::getErpCode, req.getErpCode())
                .eq(PriceAdjustFormItemPO::getRequestPriceType, req.getPriceType());
        PriceAdjustFormItemPO po = priceAdjustFormItem1Mapper.selectOne(wrapper);
        if (po == null || CollUtil.isEmpty(po.getAporovalLog())){
            return Collections.emptyList();
        }
        List<AdjustFormAporovalLogResp> aporovalLogResps = new ArrayList<>();
        for (PriceAdjustFormItemPO.AdjustFormAporovalLogPO adjustFormAporovalLogPO : po.getAporovalLog()) {
            AdjustFormAporovalLogResp adjustFormAporovalLogResp = new AdjustFormAporovalLogResp();
            BeanUtils.copyProperties(adjustFormAporovalLogPO, adjustFormAporovalLogResp);
            aporovalLogResps.add(adjustFormAporovalLogResp);
        }
        return aporovalLogResps;
    }

    @Override
    public void batchUpdate(String parentCode, List<PriceAdjustFormItemBO> itemList){
        if (StringUtils.isBlank(parentCode)){
            throw new YxtBizException("更新时， 分表键不能为空");
        }
        //todo 给parallelStream指定线程池
        itemList.parallelStream().forEach(dto -> priceAdjustFormItem1Mapper.update(null,
                Wrappers.<PriceAdjustFormItemPO>lambdaUpdate()
                        // 设置更新条件
                        .eq(PriceAdjustFormItemPO::getId, dto.getId())
                        .eq(PriceAdjustFormItemPO::getParentCode, parentCode)
                        // 设置需要更新的字段，仅当DTO中对应字段不为null时才更新
//                        .set(dto.getOrgType() != null, PriceAdjustFormItemPO::getOrgType, dto.getOrgType())
//                        .set(dto.getOrgCode() != null, PriceAdjustFormItemPO::getOrgCode, dto.getOrgCode())
                        .set(dto.getOrgName() != null, PriceAdjustFormItemPO::getOrgName, dto.getOrgName())
                        .set(dto.getRequestPrice() != null, PriceAdjustFormItemPO::getRequestPrice, dto.getRequestPrice())
                        .set(dto.getRequestStartTime() != null, PriceAdjustFormItemPO::getRequestStartTime, dto.getRequestStartTime())
                        .set(dto.getRequestEndTime() != null, PriceAdjustFormItemPO::getRequestEndTime, dto.getRequestEndTime())
                        .set(dto.getStatus() != null, PriceAdjustFormItemPO::getStatus, dto.getStatus())
                        .set(dto.getAporovalPrice() != null, PriceAdjustFormItemPO::getAporovalPrice, dto.getAporovalPrice())
                        .set(dto.getAporovalStartTime() != null, PriceAdjustFormItemPO::getAporovalStartTime, dto.getAporovalStartTime())
                        .set(dto.getAporovalEndTime() != null, PriceAdjustFormItemPO::getAporovalEndTime, dto.getAporovalEndTime())
                        .set(dto.getAporovalTime() != null, PriceAdjustFormItemPO::getAporovalTime, dto.getAporovalTime())
                        .set(dto.getAporovalStatus() != null, PriceAdjustFormItemPO::getAporovalStatus, dto.getAporovalStatus())
                        .set(dto.getDescs() != null, PriceAdjustFormItemPO::getDescs, dto.getDescs())
//                        .set(dto.getCreateName() != null, PriceAdjustFormItemPO::getCreateName, dto.getCreateName())
//                        .set(dto.getCrateTime() != null, PriceAdjustFormItemPO::getCrateTime, dto.getCrateTime())
                        .set(dto.getModifyName() != null, PriceAdjustFormItemPO::getModifyName, dto.getModifyName())
//                        .set(dto.getModifyTime() != null, PriceAdjustFormItemPO::getModifyTime, dto.getModifyTime())
                        .set(dto.getExecStatus() != null, PriceAdjustFormItemPO::getExecStatus, dto.getExecStatus())
                        .set(dto.getScope() != null, PriceAdjustFormItemPO::getScope, dto.getScope())
                        .set(dto.getRequestPriceType() != null, PriceAdjustFormItemPO::getRequestPriceType, dto.getRequestPriceType())
                        .set(dto.getErpCode() != null, PriceAdjustFormItemPO::getErpCode, dto.getErpCode())
                        .set(dto.getCompetitorName() != null, PriceAdjustFormItemPO::getCompetitorName, dto.getCompetitorName())
                        .set(dto.getCompetitorPrice() != null, PriceAdjustFormItemPO::getCompetitorPrice, dto.getCompetitorPrice())
                        .set(dto.getCompetitorImg() != null, PriceAdjustFormItemPO::getCompetitorImg, dto.getCompetitorImg())
                        .set(dto.getCancelStatus() != null, PriceAdjustFormItemPO::getCancelStatus, dto.getCancelStatus())
                        .set(dto.getIsValid() != null, PriceAdjustFormItemPO::getIsValid, dto.getIsValid())
                        .set(CollectionUtils.isNotEmpty(dto.getAporovalLog()), PriceAdjustFormItemPO::getAporovalLog, dto.getAporovalLog())));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCancelItem(String parentCode, List<Long> ids, String userName){
        if (StringUtils.isBlank(parentCode)){
            throw new YxtBizException("更新时， 分表键不能为空");
        }
        Lists.partition(ids, 500).parallelStream().forEach(partition ->
                RetryUtils.retryReVoid(3, 100, () ->{
                    priceAdjustFormItem1Mapper.update(null,
                            Wrappers.<PriceAdjustFormItemPO>lambdaUpdate()
                                    .set(PriceAdjustFormItemPO::getCancelStatus, CancelStatusEnum.PARTIALLY_CANCELED)
                                    .set(PriceAdjustFormItemPO::getModifyName, userName)
                                    .eq(PriceAdjustFormItemPO::getParentCode, parentCode)
                                    .in(PriceAdjustFormItemPO::getId, partition));
                    return null;
                }));
    }

    @Override
    public SearchAfterBO<PriceAdjustFormItemBO> list(PriceAdjustFormItemSearchBO searchBO) {

        List<PriceAdjustFormItemPO> list = priceAdjustFormItem1Mapper.selectList(Wrappers.<PriceAdjustFormItemPO>lambdaQuery()

                .eq(searchBO.getParentCode() != null, PriceAdjustFormItemPO::getParentCode, searchBO.getParentCode())
                .in(CollectionUtils.isNotEmpty(searchBO.getIds()), PriceAdjustFormItemPO::getId, searchBO.getIds())

                .eq(searchBO.getIsValid() != null, PriceAdjustFormItemPO::getIsValid, searchBO.getIsValid())
                .eq(searchBO.getStatus() != null, PriceAdjustFormItemPO::getStatus, searchBO.getStatus())
                .eq(searchBO.getCancelStatus() != null, PriceAdjustFormItemPO::getCancelStatus, searchBO.getCancelStatus())
                .apply(searchBO.getCurrentKV() != null && searchBO.getCurrentKV().nonNull(), "{} >= {}", searchBO.getCurrentKV().getKey(), searchBO.getCurrentKV().getValue())
                .last(searchBO.getBatchSize() != null, "limit " + searchBO.getBatchSize()));

        SearchAfterBO<PriceAdjustFormItemBO> result = new SearchAfterBO<>();
        result.setList(BeanUtil.copyToList(list, PriceAdjustFormItemBO.class));
        if  (searchBO.getCurrentKV() != null && searchBO.getCurrentKV().nonNull() && !list.isEmpty()) {
            result.setSortKV(new SearchAfterBO.SortKV(searchBO.getCurrentKV().getKey(), list.get(list.size() - 1).getId()));
        }

        return result;
    }


}
