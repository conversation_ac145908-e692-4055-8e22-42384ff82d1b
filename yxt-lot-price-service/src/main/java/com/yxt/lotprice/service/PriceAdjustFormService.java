package com.yxt.lotprice.service;

import com.yxt.lang.dto.api.PageDTO;
import com.yxt.lotprice.service.adjustbase.model.bo.ApprovalFlowDefinitionBO;
import com.yxt.lotprice.service.model.dto.b.request.AdjustFormPageReq;
import com.yxt.lotprice.service.model.dto.b.request.CopyAdjustFormReq;
import com.yxt.lotprice.service.model.dto.b.request.InvalidAdjustFormReq;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustFormSaveReq;
import com.yxt.lotprice.service.model.dto.b.response.AdjustFormInfoResp;
import com.yxt.lotprice.service.model.dto.b.response.AdjustFormPageResp;
import com.yxt.lotprice.service.model.dto.b.response.CopyAdjustFormResp;
import com.yxt.lotprice.service.model.enums.AdjustFlowType;
import com.yxt.lotprice.service.model.enums.AdjustScopeType;
import com.yxt.lotprice.service.model.enums.AdjustType;
import com.yxt.org.read.opensdk.emp.dto.response.EmployeeScrollResDTO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormImportGroupBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormImportStoreBO;
import com.yxt.lotprice.service.model.dto.b.request.*;
import com.yxt.lotprice.service.model.dto.b.response.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 15:06
 */
public interface PriceAdjustFormService {

    Boolean savePriceAdjustForm(PriceAdjustFormSaveReq req, String userName,String userId);
    void saveFormBatch(String userName, String userId, List<PriceAdjustFormSaveReq> formList);
    PageDTO<AdjustFormPageResp> page(AdjustFormPageReq req, String userId);

    PageDTO<AdjustFormPageResp> pageICreate(AdjustFormPageReq req, String userId);
    PageDTO<AdjustFormPageResp> pageIApprove(AdjustFormPageReq req, String userId);
    PageDTO<AdjustFormPageResp> pageIFoucs(AdjustFormPageReq req, String userId);


    Boolean invalidPriceAdjustForm(InvalidAdjustFormReq req, String userName, String userId);

    AdjustFormInfoResp getAdjustFormInfo(String code, String userName,String userId);


    void importCompanyPrice(MultipartFile multipartFile, String userName, String userId);
    void importGroupPrice(MultipartFile multipartFile, String userName, String userId);
    void importStorePrice(MultipartFile multipartFile, String userName, String userId);

    CopyAdjustFormResp copyAdjustForm(CopyAdjustFormReq req, String userId);

    ApprovalFlowDefinitionBO locateAdjustFormFlowDefinition(EmployeeScrollResDTO.EmployeeResDTO userInfo, String companyCode, AdjustScopeType scope, AdjustFlowType flowType, AdjustType type);

    List<PriceAdjustFormSaveReq> splitFormAndLocateApprovalDefinition(PriceAdjustFormSaveReq req, String userId);

    CurrentUserAdjustScopeResp getCurrentUserAdjustScope(String userId);

    List<CurrentUserCompanyResp> currentUserCompany(String userId);

    List<AdjustFormAporovalLogResp> getAdjustFormAporovalLog(AdjustFormAporovalLogReq req);

    List<SelectorDTO> getAdjustFormEnum(AdjustFormEnumReq req);

    Boolean invalidPriceAdjustFormItem(InvalidAdjustFormReq req, String userName, String userId);

    Boolean audit(PriceAdjustFormSaveReq req, String userId);

    void importSingleStoreAudit(MultipartFile file,String userId,String userName);
}
