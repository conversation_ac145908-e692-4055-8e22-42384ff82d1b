package com.yxt.lotprice.service.calculation;

import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;

import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/05/21 16:34
 * Author: qs
 */
public interface PriceAdjustFormItemDetailService {

    /**
     * 保存调价单明细数据到price_adjust_form_item_detail表
     *
     * @param storeCode 门店编码
     * @param priceGroupCode 价格组编码
     * @return 是否成功
     */
    List<PriceAdjustFormItemDetailBO> listByStoreAndOrgCode(String storeCode, String priceGroupCode, PriceTypeEnum priceTypeEnum);

    /**
     * 保存调价单明细数据到price_adjust_form_item_detail表
     *
     * @param formItemBO 调价单明细
     */
    void saveFormItemDetail(PriceAdjustFormItemBO formItemBO);

    /**
     * 保存调价单明细数据到price_adjust_form_item_detail表
     *
     * @param storeCode 门店编码
     * @param formItemBOList 调价单明细集合
     */
    void saveFormItemDetailSingleStore(String storeCode, List<PriceAdjustFormItemBO> formItemBOList);

    /**
     * 按照id删除数据
     *
     * @param storeCode 门店编码
     * @param ids id集合
     */
    void deleteByStoreAndIds(String storeCode, Collection<Long> ids);

    /**
     * 查询有效的调价单价格明细
     * @return 明细集合
     */
    List<PriceAdjustFormItemDetailBO> listEnableByStoreCodeErpCodes(PriceTypeEnum priceTypeEnum, String storeCode, Collection<String> erpCodes);
}
