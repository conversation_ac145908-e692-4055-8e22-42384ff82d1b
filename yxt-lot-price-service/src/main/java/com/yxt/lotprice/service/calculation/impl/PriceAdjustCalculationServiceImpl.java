package com.yxt.lotprice.service.calculation.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.lang.util.ExLogger;
import com.yxt.lotprice.common.sharding.AbstractShardingAlgorithm;
import com.yxt.lotprice.common.sharding.CommonCalculateSuffix;
import com.yxt.lotprice.service.calculation.PriceAdjustCalculationService;
import com.yxt.lotprice.service.PriceAdjustFormItemService;
import com.yxt.lotprice.service.calculation.PriceAdjustFormItemDetailService;
import com.yxt.lotprice.service.calculation.PriceAdjustResultsService;
import com.yxt.lotprice.service.model.bo.*;
import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.model.enums.DimensionEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.model.enums.log.PriceCalculateSourceEnum;
import com.yxt.lotprice.service.model.enums.log.PriceSourceEnum;
import com.yxt.lotprice.service.mq.PricePublishService;
import com.yxt.lotprice.service.utils.PriceCompareUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 价格调整计算Service实现类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */

@Service
public class PriceAdjustCalculationServiceImpl implements PriceAdjustCalculationService {

    @Resource
    private PriceAdjustFormItemService priceAdjustFormItemService;
    @Resource
    private PriceAdjustFormItemDetailService priceAdjustFormItemDetailService;
    @Resource
    private PriceAdjustResultsService priceAdjustResultsService;
    @Resource
    private PricePublishService pricePublishService;

    @Override
    public void processAdjustPriceFormItem(Long formItemId, PriceSourceEnum priceSourceEnum) {
        ExLogger.logger().info("开始处理调价单项目，ID: {}", formItemId);

        try {
            // todo qs 从price_adjust_form_item 表查询调价单项目数据 主库 按照不同类型获取，集团的特殊
            PriceAdjustFormItemBO formItemBO = priceAdjustFormItemService.getById(formItemId);
            if (formItemBO == null) {
                ExLogger.logger().error("调价单明细不存在，ID: {}", formItemId);
                // todo qs 发送告警
                return;
            }

            // 确定门店范围
            List<String> storeCodeList = priceAdjustFormItemService.getStoreCodeByDimension(formItemBO.getOrgType(), formItemBO.getOrgCode());
            if (CollectionUtils.isEmpty(storeCodeList)) {
                // 没有门店，直接完成
                ExLogger.logger().error("调价单机构获取门店为空: {}", formItemBO);
                priceAdjustFormItemService.formItemExecFinish(formItemId);
                return;
            }
            // todo qs 保存调价单明细数据到price_adjust_form_item_detail表 非门店维度需要查询门店商品经营范围做过滤
            priceAdjustFormItemDetailService.saveFormItemDetail(formItemBO);

            // 计算门店价格
            List<PriceResultsBO> priceResultsBOList = new ArrayList<>();
            storeCodeList.forEach(storeCode -> {
                priceResultsBOList.addAll(calculatePriceResult(formItemBO.getRequestPriceType(), storeCode, Collections.singletonList(formItemBO.getErpCode())));
            });

            // 保存计算的价格结果，获取下发数据 todo qs
            List<PriceResultsBO> publishPriceList = savePriceResultAndGetPublishPrice(formItemBO.getErpCode(), formItemBO.getRequestPriceType(), priceResultsBOList, priceSourceEnum);

            pricePublishService.publishPrice(pricePublishService.buildPublishData(publishPriceList));
            ExLogger.logger().info("处理调价单项目成功，ID: {}", formItemId);
        } catch (Exception e) {
            ExLogger.logger().error("处理调价单项目异常，ID: {}, 异常信息: {}", formItemId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 保存结果数据 新增/更新
     * 获取待下发数据
     *
     * @return 需要下发的数据
     */
    private List<PriceResultsBO> savePriceResultAndGetPublishPrice(String erpCode, PriceTypeEnum priceType, List<PriceResultsBO> priceResultsBOList, PriceSourceEnum sourceEnum) {
        List<PriceResultsBO> publishPriceList = new ArrayList<>();
        Map<String, List<PriceResultsBO>> groupBySharding;
        if (priceResultsBOList.size() < 10000) {
            // 直接分批查询
            List<List<PriceResultsBO>> partition = ListUtils.partition(priceResultsBOList, 1000);
            groupBySharding = new HashMap<>();
            for (int i = 0; i < partition.size(); i++) {
                groupBySharding.put(String.valueOf(i), partition.get(i));
            }
        } else {
            // 按照分公司 门店hash 分组
            groupBySharding = priceResultsBOList.stream().collect(Collectors
                    // todo qs 考虑在common中增加配置获取分表数
                    .groupingBy(r -> r.getCompanyCode() + AbstractShardingAlgorithm.split
                            + CommonCalculateSuffix.calculateTableSuffix(r.getStoreCode(), 1024, 16)));
        }
        groupBySharding.forEach((k, v) -> {
            String tableName = "";
            Set<String> companyCodeSet = null;
            if (k.contains(AbstractShardingAlgorithm.split)) {
                tableName = k;
            } else {
                companyCodeSet = v.stream().map(PriceResultsBO::getCompanyCode).collect(Collectors.toSet());
            }
            List<PriceResultsBO> priceResultsBODbList = priceAdjustResultsService
                    .listByErpCodePriceTypeStoreCode(tableName, companyCodeSet, v.stream().map(PriceResultsBO::getStoreCode).collect(Collectors.toSet()), erpCode, priceType);

            publishPriceList.addAll(saveOrUpdateAndGetPublishPrice(sourceEnum, v, priceResultsBODbList));
        });

        // todo qs 待解决问题：数据库更新成功了，发送mq异常，或者部分数据更新成功了，后面更新数据异常。重试时数据已经一致，不会触发下发价格。
        //  解决方案：1. 不判断是否变化，全部下发价格 2. 判断下发状态，下发状态如果是已下发，则不下发，未下发则再次下发
        return publishPriceList;
    }

    @Override
    public List<PriceResultsBO> saveOrUpdateAndGetPublishPrice(PriceSourceEnum sourceEnum, List<PriceResultsBO> newList, List<PriceResultsBO> oldList) {
        List<PriceResultsBO> publishPriceList = new ArrayList<>();
        // 筛选更新还是新增
        List<PriceResultsBO> toInsertList = new ArrayList<>();
        List<PriceResultsUpdateBO> toUpdatePriceSegmentList = new ArrayList<>();
        List<PriceResultsUpdateBO> toUpdateSegmentList = new ArrayList<>();
        Map<String, PriceResultsBO> dbMap = oldList.stream()
                .collect(Collectors.toMap(
                        PriceResultsBO::buildUniqueKey,
                        item -> item,
                        (v1, v2) -> v1
                ));

        for (PriceResultsBO newItem : newList) {
            PriceResultsBO existingItem = dbMap.get(newItem.buildUniqueKey());

            if (existingItem == null) {
                // 数据库中没有，新增
                toInsertList.add(newItem);
                publishPriceList.add(newItem);
            } else {
                // 数据库中存在，比较是否有变化
                boolean segmentEqual = PriceCompareUtils.arePriceTimelineListsEqual(existingItem.getPriceResultSegment(), newItem.getPriceResultSegment());
                boolean priceEqual = PriceCompareUtils.priceEqual(existingItem, newItem);
                if (!priceEqual) {
                    // 近期价格有变化，价格代肯定有变化
                    newItem.setId(existingItem.getId());
                    PriceResultsUpdateBO updateBO = new PriceResultsUpdateBO();
                    BeanUtil.copyProperties(newItem, updateBO);
                    updateBO.setOldPrice(new PriceBO(existingItem.getPrice(), existingItem.getStartTime(), existingItem.getEndTime()
                            , existingItem.getNextPrice(), existingItem.getNextStartTime(), existingItem.getNextEndTime()));
                    toUpdatePriceSegmentList.add(updateBO);
                    publishPriceList.add(newItem);
                } else if (!segmentEqual) {
                    // 近期价格没变化只是价格代变化，只更新价格代，不下发价格
                    toUpdateSegmentList.add(BeanUtil.copyProperties(newItem, PriceResultsUpdateBO.class));
                    ExLogger.logger().info("计算后的数据近期价格无变化，不下发：db：{}，new：{}", JSON.toJSONString(existingItem), JSON.toJSONString(newItem));
                } else {
                    ExLogger.logger().info("计算后的数据近期价格、价格代均无变化，不更新、不下发：db：{}，new：{}", JSON.toJSONString(existingItem), JSON.toJSONString(newItem));
                }
            }
        }

        // 持久化数据
        if (CollectionUtils.isNotEmpty(toInsertList)) {
            priceAdjustResultsService.saveBatch(toInsertList, sourceEnum);
        }
        if (CollectionUtils.isNotEmpty(toUpdatePriceSegmentList)) {
            priceAdjustResultsService.updatePriceSegment(toUpdatePriceSegmentList, sourceEnum);
        }
        if (CollectionUtils.isNotEmpty(toUpdateSegmentList)) {
            priceAdjustResultsService.updatePriceSegmentOnly(toUpdateSegmentList, sourceEnum);
        }

        return publishPriceList;
    }

    @Override
    public void calculateSavePublish(PriceTypeEnum priceTypeEnum, String storeCode, List<String> erpCodeList) {
        List<PriceResultsBO> newList = calculatePriceResult(priceTypeEnum, storeCode, erpCodeList);
        List<PriceResultsBO> oldList = priceAdjustResultsService.listByStoreCodePriceTypeErpCodes(storeCode, priceTypeEnum, newList.stream().map(PriceResultsBO::getErpCode).collect(Collectors.toSet()));
        // 保存价格
        List<PriceResultsBO> publishList = saveOrUpdateAndGetPublishPrice(PriceCalculateSourceEnum.STORE_ADD, newList, oldList);
        // 下发价格
        pricePublishService.publishPrice(pricePublishService.buildPublishData(publishList));
    }

    @Override
    public List<PriceResultsBO> calculatePriceResult(PriceTypeEnum priceTypeEnum, String storeCode, List<String> erpCodeList) {
        if (StringUtils.isEmpty(storeCode) || CollectionUtils.isEmpty(erpCodeList)) {
            ExLogger.logger().warn("计算价格，参数为空，storeCode: {}, erpCodes: {}", storeCode, erpCodeList);
            throw new YxtBizException("计算价格，参数为空");
        }

        // 查询门店商品所有调价单价格明细
        List<PriceResultsBO> result = new ArrayList<>();
        List<List<String>> erpCodePartition = ListUtils.partition(erpCodeList, 100);
        erpCodePartition.forEach(erpCodes -> {
            // todo qs 是否要查主库？
            List<PriceAdjustFormItemDetailBO> priceAdjustFormItemDetailList = priceAdjustFormItemDetailService.listEnableByStoreCodeErpCodes(priceTypeEnum, storeCode, erpCodes);
            if (CollectionUtils.isEmpty(priceAdjustFormItemDetailList)) {
                return;
            }
            result.addAll(calculatePriceResult(priceAdjustFormItemDetailList));
        });

        return result;
    }

    @Override
    public List<PriceResultsBO> calculatePriceResult(List<PriceAdjustFormItemDetailBO> priceDetailList) {
        return priceDetailList.stream()
                // 第1层：按价格类型分组
                .collect(Collectors.groupingBy(PriceAdjustFormItemDetailBO::getPriceType))
                .entrySet().stream()
                .flatMap(priceTypeEntry -> {
                    PriceTypeEnum priceType = priceTypeEntry.getKey();
                    Map<String, List<PriceAdjustFormItemDetailBO>> storeErpGroup = priceTypeEntry.getValue().stream()
                            // 第2层：按门店+商品分组
                            .collect(Collectors.groupingBy(b -> b.getStoreCode() + b.getErpCode()));

                    return storeErpGroup.values().stream().map(detailList -> {
                        PriceAdjustFormItemDetailBO first = detailList.get(0); // 提取代表元素

                        PriceResultsBO bo = new PriceResultsBO();
                        bo.setErpCode(first.getErpCode());
                        bo.setStoreCode(first.getStoreCode());
                        bo.setStoreId(first.getStoreId());
                        bo.setCompanyCode(first.getCompanyCode());
                        bo.setPriceType(priceType);

                        // 计算价格代
                        List<PriceTimelineSegmentBO> segments = calculatePriceSegmentSingle(detailList);
                        if (CollectionUtils.isNotEmpty(segments)) {
                            bo.setPrice(segments.get(0).getPrice());
                            bo.setStartTime(segments.get(0).getStart());
                            bo.setEndTime(segments.get(0).getEnd());

                            if (segments.size() > 1) {
                                bo.setNextPrice(segments.get(1).getPrice());
                                bo.setNextStartTime(segments.get(1).getStart());
                                bo.setNextEndTime(segments.get(1).getEnd());
                            }

                            bo.setPriceResultSegment(segments);
                        }

                        return bo;
                    });
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<PriceTimelineSegmentBO> calculatePriceSegmentSingle(List<PriceAdjustFormItemDetailBO> priceDetailList) {
        // 优先级按调价维度以此类推，门店 → 价格组 → 公司
        // 同一调价维度时优先取最后审批的单据
        List<CalculatePriceEvent> events = new ArrayList<>();

        for (PriceAdjustFormItemDetailBO item : priceDetailList) {
            if (!CancelStatusEnum.NOT_CANCELED.equals(item.getEnableStatus())) continue;
            events.add(new CalculatePriceEvent(item.getStartTime(), true, item));
            events.add(new CalculatePriceEvent(item.getEndTime().plusDays(1), false, item));
        }

        // 时间升序排序，开始事件排前
        events.sort(Comparator
                .comparing(CalculatePriceEvent::getDate)
                .thenComparing(e -> !e.isStart()));

        // 优先级比较器：门店 > 价格组 > 分公司，审批时间倒序
        TreeSet<PriceAdjustFormItemDetailBO> activeSet = new TreeSet<>(
                Comparator
                        .comparingInt((PriceAdjustFormItemDetailBO o) -> o.getOrgType().getPriority())
                        .thenComparing(PriceAdjustFormItemDetailBO::getAporovalTime, Comparator.reverseOrder())
                        .thenComparing(PriceAdjustFormItemDetailBO::getFormItemNo) // 保证一致性
        );

        List<PriceTimelineSegmentBO> segments = new ArrayList<>();
        LocalDate prevDate = null;

        for (CalculatePriceEvent event : events) {
            LocalDate currentDate = event.getDate();

            if (prevDate != null && prevDate.isBefore(currentDate) && !activeSet.isEmpty()) {
                PriceAdjustFormItemDetailBO main = activeSet.first();
                Set<String> allFormNos = activeSet.stream()
                        .map(PriceAdjustFormItemDetailBO::getFormItemNo)
                        .collect(Collectors.toSet());

                segments.add(new PriceTimelineSegmentBO(
                        prevDate,
                        currentDate.minusDays(1),
                        main.getPrice(),
                        main.getFormNo(),
                        main.getFormItemNo(),
                        main.getOrgType(),
                        allFormNos, Collections.emptyList()
                ));
            }

            if (event.isStart()) {
                activeSet.add(event.getSource());
            } else {
                activeSet.remove(event.getSource());
            }

            prevDate = currentDate;
        }

        // 合并返回
        return mergeTimelineSegments(segments);
    }

    /**
     * 合并相同价格的时间
     *
     * @param rawSegments 计算好的每段时间
     * @return 合并的时间
     */
    private List<PriceTimelineSegmentBO> mergeTimelineSegments(List<PriceTimelineSegmentBO> rawSegments) {
        if (rawSegments.isEmpty()) return Collections.emptyList();

        List<PriceTimelineSegmentBO> result = new ArrayList<>();
        PriceTimelineSegmentBO current = rawSegments.get(0);

        for (int i = 1; i < rawSegments.size(); i++) {
            PriceTimelineSegmentBO next = rawSegments.get(i);

            boolean isAdjacent = current.getEnd().plusDays(1).equals(next.getStart());
            boolean samePrice = current.getPrice().compareTo(next.getPrice()) == 0;

            if (isAdjacent && samePrice) {
                // 合并时间段 + 单号集合
                if (CollectionUtils.isEmpty(current.getMergeList())) {
                    current.setMergeList(new ArrayList<>());
                    current.getMergeList().add(BeanUtil.copyProperties(current, PriceTimelineSegmentBO.PriceTimeSegment.class));
                }
                current.getMergeList().add(BeanUtil.copyProperties(next, PriceTimelineSegmentBO.PriceTimeSegment.class));
                current.setEnd(next.getEnd());
                current.getAllFormItemNos().addAll(next.getAllFormItemNos());
            } else {
                result.add(current);
                current = next;
            }
        }

        result.add(current); // 添加最后一段
        return result;
    }

    public static void main(String[] args) {
        List<PriceAdjustFormItemDetailBO> priceDetailList = new ArrayList<>();
        PriceAdjustFormItemDetailBO b0 = new PriceAdjustFormItemDetailBO();
        PriceAdjustFormItemDetailBO b1 = new PriceAdjustFormItemDetailBO();
        PriceAdjustFormItemDetailBO b2 = new PriceAdjustFormItemDetailBO();
        PriceAdjustFormItemDetailBO b3 = new PriceAdjustFormItemDetailBO();
        priceDetailList.add(b0);
        priceDetailList.add(b1);
        priceDetailList.add(b2);
        priceDetailList.add(b3);

        b0.setFormItemNo("000");
        b0.setEnableStatus(CancelStatusEnum.NOT_CANCELED);
        b0.setPriceType(PriceTypeEnum.RETAIL);
        b0.setPrice(new BigDecimal("10"));
        b0.setOrgType(DimensionEnum.PRICE_GROUP);
        b0.setStartTime(LocalDate.of(2025, 5, 22));
        b0.setEndTime(LocalDate.of(2025, 6, 25));
        b0.setAporovalTime(LocalDateTime.of(LocalDate.of(2025, 6, 25), LocalTime.of(22, 11, 11)));

        b1.setFormItemNo("111");
        b1.setEnableStatus(CancelStatusEnum.NOT_CANCELED);
        b1.setPriceType(PriceTypeEnum.RETAIL);
        b1.setPrice(new BigDecimal("20"));
        b1.setOrgType(DimensionEnum.STORE);
        b1.setStartTime(LocalDate.of(2025, 5, 24));
        b1.setEndTime(LocalDate.of(2025, 5, 28));
        b1.setAporovalTime(LocalDateTime.of(LocalDate.of(2025, 6, 25), LocalTime.of(22, 11, 11)));


        b2.setFormItemNo("222");
        b2.setEnableStatus(CancelStatusEnum.NOT_CANCELED);
        b2.setPriceType(PriceTypeEnum.RETAIL);
        b2.setPrice(new BigDecimal("30"));
        b2.setOrgType(DimensionEnum.COMPANY);
        b2.setStartTime(LocalDate.of(2025, 5, 27));
        b2.setEndTime(LocalDate.of(2025, 8, 5));
        b2.setAporovalTime(LocalDateTime.of(LocalDate.of(2025, 6, 25), LocalTime.of(22, 11, 11)));

        b3.setFormItemNo("333");
        b3.setEnableStatus(CancelStatusEnum.NOT_CANCELED);
        b3.setPriceType(PriceTypeEnum.RETAIL);
        b3.setPrice(new BigDecimal("20"));
        b3.setOrgType(DimensionEnum.STORE);
        b3.setStartTime(LocalDate.of(2025, 5, 27));
        b3.setEndTime(LocalDate.of(2025, 7, 5));
        b3.setAporovalTime(LocalDateTime.of(LocalDate.of(2025, 6, 25), LocalTime.of(22, 11, 12)));

//        List<PriceTimelineSegmentBO> re = calculate(priceDetailList);
//        List<PriceTimelineSegmentBO> re1 = mergeTimelineSegments(re);
//        System.out.println(re);
    }

    @AllArgsConstructor
    @Getter
    public static class CalculatePriceEvent {
        private LocalDate date;
        private boolean isStart;
        private PriceAdjustFormItemDetailBO source;
    }
}
