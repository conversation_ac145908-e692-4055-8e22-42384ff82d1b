package com.yxt.lotprice.service.impl;

import com.yxt.lang.exception.YxtBizException;
import com.yxt.lotprice.service.PriceAdjustFormItemService;
import com.yxt.lotprice.service.cache.AllStoreInfoCache;
import com.yxt.lotprice.service.cache.OrgInfoCacheUtils;
import com.yxt.lotprice.service.manager.iface.PriceAdjustFormItemManager;
import com.yxt.lotprice.service.manager.sdk.dto.StoreDTO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemSearchBO;
import com.yxt.lotprice.service.model.dto.third.response.SysOrganizationDTO;
import com.yxt.lotprice.service.model.enums.DimensionEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Since: 2025/05/21 17:25
 * Author: qs
 */

@Service
public class PriceAdjustFormItemServiceImpl implements PriceAdjustFormItemService {

    @Resource
    private PriceAdjustFormItemManager priceAdjustFormItemManager;

    @Resource
    private AllStoreInfoCache allStoreInfoCache;

    @Override
    public PriceAdjustFormItemBO getById(Long id) {
        return null;
    }

    @Override
    public List<String> getStoreCodeByDimension(DimensionEnum dimensionEnum, String orgCode) {
        if (DimensionEnum.STORE.equals(dimensionEnum)) {
            return Collections.singletonList(orgCode);
        } else if (DimensionEnum.PRICE_GROUP.equals(dimensionEnum)) {
            // todo qs 价格组的门店 调用接口/本地缓存筛选
            return Collections.emptyList();
        } else if (DimensionEnum.COMPANY.equals(dimensionEnum)) {
            return OrgInfoCacheUtils.getStoreListByOrgId(orgCode).stream().map(SysOrganizationDTO::getOrCode).collect(Collectors.toList());
        } else {
            throw new YxtBizException("调价单新增机构类型，需要新增逻辑");
        }
    }

    @Override
    public boolean formItemExecFinish(Long id) {
        return false;
    }

    @Override
    public List<PriceAdjustFormItemBO> listAllEnableByStoreCodeAndType(String storeCode, PriceTypeEnum priceTypeEnum) {
        StoreDTO storeDTO = allStoreInfoCache.getStore(storeCode);
        if (storeDTO == null) {
            throw new YxtBizException("收到门店新增消息，查询到门店信息为空");
        }

        List<PriceAdjustFormItemBO> itemBOList = priceAdjustFormItemManager.listEnableByPriceTypeOrgCode(storeCode, priceTypeEnum);
        // 价格组的调价单
        if (StringUtils.isEmpty(storeDTO.getPricegroupIdNumber())) {
            itemBOList.addAll(priceAdjustFormItemManager.listEnableByPriceTypeOrgCode(storeDTO.getPricegroupIdNumber(), priceTypeEnum));
        }
        // 分公司的调价单
        if (StringUtils.isEmpty(storeDTO.getBranchCode())) {
            itemBOList.addAll(priceAdjustFormItemManager.listEnableByPriceTypeOrgCode(storeDTO.getBranchCode(), priceTypeEnum));
        }
        return itemBOList;
    }

    @Override
    public List<PriceAdjustFormItemBO> listEnableByPriceTypeOrgCode(String orgCode, PriceTypeEnum priceTypeEnum) {
        return priceAdjustFormItemManager.listEnableByPriceTypeOrgCode(orgCode, priceTypeEnum);
    }
}
