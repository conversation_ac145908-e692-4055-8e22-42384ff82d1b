package com.yxt.lotprice.service.manager.iface;

import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemDetailBO;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import java.util.Collection;
import java.util.List;

/**
 * Since: 2025/05/22 9:51
 * Author: qs
 */
public interface PriceAdjustFormItemDetailManager {

    /**
     * 查询门店商品调价单价格明细
     * @param storeCode 门店编码
     * @param erpCodes 商品编码
     * @return 明细集合
     */
    List<PriceAdjustFormItemDetailBO> listEnableByStoreCodeErpCodes(PriceTypeEnum priceTypeEnum, String companyCode, String storeCode, Collection<String> erpCodes);

    /**
     * 查询门店价格组类型生成的价格明细
     * @param storeCode 门店编码
     * @param priceGroupCode 价格组编码
     * @param priceTypeEnum 价格类型
     * @return boList
     */
    List<PriceAdjustFormItemDetailBO> listByStoreAndOrgCode(String companyCode, String storeCode, String priceGroupCode, PriceTypeEnum priceTypeEnum);

    /**
     * 批量插入
     * @param records
     * @return
     */
    boolean insertBatch(Collection<PriceAdjustFormItemDetailBO> records);

    /**
     * 批量插入更新（ON DUPLICATE KEY UPDATE）
     *
     * @param records 记录
     */
    void insertBatchOnDuplicateKeyUpdate(Collection<PriceAdjustFormItemDetailBO> records);

    /**
     * 按照id删除数据
     *
     * @param storeCode 门店编码
     * @param ids id集合
     */
    int deleteByStoreAndIds(String companyCode, String storeCode, Collection<Long> ids);

}
