package com.yxt.lotprice.service.manager.iface;

import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemSearchBO;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemBO;
import com.yxt.lotprice.service.model.bo.SearchAfterBO;
import com.yxt.lotprice.service.model.dto.b.request.AdjustFormAporovalLogReq;
import com.yxt.lotprice.service.model.bo.PriceAdjustFormItemBO;
import com.yxt.lotprice.service.model.dto.b.request.PriceAdjustFormSaveReq;
import com.yxt.lotprice.service.model.dto.b.response.AdjustFormAporovalLogResp;
import com.yxt.lotprice.service.model.dto.b.response.PriceAdjustFormItemResp;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 16:02
 */
public interface PriceAdjustFormItemManager {
    void savePriceAdjustFormItem(PriceAdjustFormSaveReq req, String userName);

    void updatePriceAdjustFormItem(PriceAdjustFormSaveReq req, String userName);

    List<PriceAdjustFormItemResp> getPriceAdjustFormItem(String parentCode);

    List<AdjustFormAporovalLogResp> getAdjustFormAporovalLog(AdjustFormAporovalLogReq req);

    void batchUpdate(String parentCode, List<PriceAdjustFormItemBO> itemList);

    void batchCancelItem(String parentCode, List<Long> ids, String userName);

    SearchAfterBO<PriceAdjustFormItemBO> list(PriceAdjustFormItemSearchBO searchBO);

    /**
     * 根据门店查询所有调价单明细
     * 分批查询
     * @return
     */
    List<PriceAdjustFormItemBO> listEnableByPriceTypeOrgCode(String orgCode, PriceTypeEnum priceTypeEnum);

    /**
     * 获取调价单商品详情
     */
    List<PriceAdjustFormItemBO> listByCodeAndErpCodes(String code,List<String> erpCodeList);
}
