package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.adjustbase.model.bo.ApprovalFlowDefinitionBO;
import com.yxt.lotprice.service.model.enums.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 调价申请单表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Data
public class PriceAdjustFormBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 调价单编码
     */
    private String code;

    /**
     * 调价单名称
     */
    private String name;

    /**
     * 项目属性-集团/公司
     */
    private AdjustScopeType scope;

    /**
     * 定价/调价维度，STORE-门店,PRICE_GROUP-价格组,COMPANY-子公司
     */
    private DimensionEnum dimension;

    /**
     * 作废状态:未作废，部分作废，已作废
     */
    private CancelStatusEnum cancelStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 创建人id
     */
    private Integer createUserId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 定价/调价类别：定价，调价
     */
    private AdjustType priceOpsType;

    /**
     * 审批流id
     */
    private Long flowDefinitionId;

    /**
     * 审批流快照
     */
    ApprovalFlowDefinitionBO flowDefinition;

    /**
     * 调价单状态，审核中，已审核，草稿，已驳回，已撤销，待审核
     */
    private AdjustFormAuditStatus status;

    /**
     * 审核通过时间
     */
    private LocalDateTime approvalTime;

    /**
     * 报批人类别（用于前端查询）
     */
    private AdjustRoleType createUserRole;
}
