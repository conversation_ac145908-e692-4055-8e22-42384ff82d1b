package com.yxt.lotprice.service.model.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 调价单导入
 * @Author: wangyasi
 * @date: 2025/5/27 9:56
 */
@Data
public class PriceAdjustFormImportBaseBO{

    @ExcelProperty("商品编码")
    private String erpCode;
    // 商品名称 不导入
    @ExcelProperty("商品名称")
    private String erpName;

    /**
     * 零售价
     */
    @ExcelProperty("申请零售价")
    private BigDecimal retailPrice;

    @ExcelProperty(value = "申请调整开始时间",index = 5)
    private LocalDate retailStartTime;

    @ExcelProperty(value = "申请调整结束时间",index = 6)
    private LocalDate retailEndTime;

    /**
     * 会员价
     */
    @ExcelProperty("申请会员价格")
    private BigDecimal vipPrice;

    @ExcelProperty(value = "申请调整开始时间",index = 8)
    private LocalDate vipStartTime;

    @ExcelProperty(value = "申请调整结束时间",index = 9)
    private LocalDate vipEndTime;

    /**
     * 慢病价
     */
    @ExcelProperty("申请慢病价格")
    private BigDecimal chronicPrice;

    @ExcelProperty(value = "申请调整开始时间",index = 11)
    private LocalDate chronicStartTime;

    @ExcelProperty(value = "申请调整结束时间",index = 12)
    private LocalDate chronicEndTime;


//    public void setRetailEndTime(LocalDateTime retailEndTime) {
//        this.retailEndTime = retailEndTime;
//        if (retailEndTime.getHour() == 0 && retailEndTime.getMinute() == 0 && retailEndTime.getSecond() == 0){
//            LocalDateTime endDate = retailEndTime.withHour(23).withMinute(59).withSecond(59);
//            this.retailEndTime = endDate;
//        }
//    }
//
//    public void setVipEndTime(LocalDateTime vipEndTime) {
//        this.vipEndTime = vipEndTime;
//        if (vipEndTime.getHour() == 0 && vipEndTime.getMinute() == 0 && vipEndTime.getSecond() == 0){
//            LocalDateTime endDate = vipEndTime.withHour(23).withMinute(59).withSecond(59);
//            this.vipEndTime = endDate;
//        }
//    }
//
//    public void setChronicEndTime(LocalDateTime chronicEndTime) {
//        this.chronicEndTime = chronicEndTime;
//        if (chronicEndTime.getHour() == 0 && chronicEndTime.getMinute() == 0 && chronicEndTime.getSecond() == 0){
//            LocalDateTime endDate = chronicEndTime.withHour(23).withMinute(59).withSecond(59);
//            this.chronicEndTime = endDate;
//        }
//    }
}
