package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.dto.mq.rocketmq.AdjustPriceFormItemDTO;
import com.yxt.lotprice.service.model.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * Since: 2025/05/21 14:40
 * Author: qs
 */

@Data
public class PriceAdjustFormItemBO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 调价单编码
     */
    private String parentCode;

    /**
     * 机构类型：分公司，价格组，门店
     */
    private DimensionEnum orgType;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 申请调/定价格
     */
    private BigDecimal requestPrice;

    /**
     * 申请调/定价生效开始时间
     */
    private LocalDateTime requestStartTime;

    /**
     * 申请调/定价生效结束时间
     */
    private LocalDateTime requestEndTime;

    /**
     * 审批结果：暂存/审批中/审批通过/驳回，等
     */
    private AdjustFormAuditStatus status;

    /**
     * 审批通过调/定价格
     */
    private BigDecimal aporovalPrice;

    /**
     * 审批通过调/定价开始时间
     */
    private LocalDate aporovalStartTime;

    /**
     * 审批通过调/定价结束时间
     */
    private LocalDate aporovalEndTime;

    /**
     * 审批通过时间
     */
    private LocalDateTime aporovalTime;
    /**
     * 审核状态 1:同意 0:不同意
     */
    private Integer aporovalStatus;

    /**
     * 审批备注
     */
    private String descs;

    /**
     * 创建人/调价单创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private LocalDateTime crateTime;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;

    /**
     * 调价单执行状态：未开始，进行中，已完成，失败
     */
    private ItemExecStatusEnum execStatus;

    /**
     * 集团/分公司
     */
    private AdjustScopeType scope;

    /**
     * 申请调价类型：（会员价，零售价，慢病价）
     */
    private PriceTypeEnum requestPriceType;

    /**
     * 商品编码
     */
    private String erpCode;

    /**
     * 审批记录，json字符串，记录每一步操作步骤的前后的值，以及操作人
     */
    private List<AdjustPriceFormItemDTO.AdjustFormAporovalLogBO> aporovalLog;
    /**
     * 竞对名称
     */
    private String competitorName;
    /**
     * 竞对价格
     */
    private String competitorPrice;
    /**
     * 竞对图片
     */
    private String competitorImg;

    /**
     * 作废状态
     */
    private CancelStatusEnum cancelStatus;

    /**
     * 是否可用（主单审核通过，子单同意，子单未作废，主单未作废）
     */
    private Integer isValid;
    @Data
    public static class AdjustFormAporovalLogBO {
        @ApiModelProperty("审批通过价格")
        private BigDecimal aporovalPrice;
        @ApiModelProperty("审批通过调/定价开始时间")
        private LocalDateTime aporovalStartTime;
        @ApiModelProperty("审批通过调/定价结束时间")
        private LocalDateTime aporovalEndTime;
        @ApiModelProperty("审批备注")
        private String descs;
        @ApiModelProperty("审批结果 1:同意 0:不同意")
        private Integer aporovalStatus;

        @ApiModelProperty("审批人")
        private String aporovalUserName;
        @ApiModelProperty("审批人工号")
        private String aporovalUserCode;
    }
}
