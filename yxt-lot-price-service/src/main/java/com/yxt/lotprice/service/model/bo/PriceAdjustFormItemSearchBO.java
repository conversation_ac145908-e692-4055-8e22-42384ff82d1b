package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Since: 2025/05/21 14:40
 * Author: qs
 */

@Data
public class PriceAdjustFormItemSearchBO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 索引条件
     */
     // 调价单编码
    private String parentCode;
    //id列表
    private List<Long> ids;


    /**
     * 非索引条件
     */
    //是否生效
    private Integer isValid;

    //是否同意
    private Integer status;

    //是否部分作废
    private CancelStatusEnum cancelStatus;

    /**
     * 每批大小
     */
    private Long batchSize;

    /**
     * 如果设置了，本次就会从key字段的value值开始查询
     */
    SearchAfterBO.SortKV currentKV;

}
