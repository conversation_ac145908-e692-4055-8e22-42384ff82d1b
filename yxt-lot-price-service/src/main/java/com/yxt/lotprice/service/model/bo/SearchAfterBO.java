package com.yxt.lotprice.service.model.bo;

import com.yxt.lotprice.service.model.enums.CancelStatusEnum;
import com.yxt.lotprice.service.model.enums.DimensionEnum;
import com.yxt.lotprice.service.model.enums.ItemExecStatusEnum;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;


/**
 * Since: 2025/05/21 14:40
 * Author: qs
 */

@Data
public class SearchAfterBO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    List<T> list;
    SortKV sortKV;

    @Data
    public static class SortKV implements Serializable {
        String key;
        Object value;

        public SortKV(String key, Object value) {
            this.key = key;
            this.value = value;
        }

        public SortKV() {
        }

        public boolean nonNull(){
            return StringUtils.isNotBlank(key) && value != null;
        }
    }
}
