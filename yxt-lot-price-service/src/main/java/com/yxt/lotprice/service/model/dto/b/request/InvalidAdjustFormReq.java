package com.yxt.lotprice.service.model.dto.b.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/27 10:51
 */
@Data
@ApiModel("作废调价单请求参数")
public class InvalidAdjustFormReq {
    @ApiModelProperty("调价单编码")
    @NotEmpty(message = "调价单编码不能为空")
    private String code;

    @ApiModelProperty("调价单明细id")
    private List<Long> itemIds;

    @ApiModelProperty("备注")
    private String remark;
}
