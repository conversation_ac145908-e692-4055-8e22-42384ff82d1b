package com.yxt.lotprice.service.model.dto.b.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.lotprice.service.adjustbase.model.bo.ApprovalFlowDefinitionBO;
import com.yxt.lotprice.service.model.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/26 15:11
 */
@Data
@ApiModel("调价单审核请求参数")
public class PriceAdjustFormAuditReq {
    @ApiModelProperty("调价单编码")
    @NotBlank(message = "调价单编码不能为空")
    private String code;
}
