package com.yxt.lotprice.service.model.dto.b.response;

import com.yxt.lotprice.service.model.dto.b.request.RelateExtReq;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/19 18:19
 */
@Data
@ApiModel("审批人组织配置返回值")
public class ItemRelateApproverResp {
    @ApiModelProperty("职能类别编码")
    private String roleCode;

    @ApiModelProperty("职能类别名称")
    private String roleName;

    @ApiModelProperty("员工信息")
    private List<RelateExtReq> roleData;
}
