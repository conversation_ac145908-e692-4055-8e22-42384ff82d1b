package com.yxt.lotprice.service.model.dto.b.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yxt.batch.annotation.EnableAsyncBatchExportTask;
import com.yxt.lotprice.service.model.enums.PriceTypeEnum;
import com.yxt.lotprice.service.processor.PriceAdjustFormExportProcessor;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 调价单导出
 * @Author: wangyasi
 * @date: 2025/5/27 9:56
 */
@EnableAsyncBatchExportTask(businessType = "batch_store_spec_b2b_export", taskName = "单店导出", fileName = "门店调价单店导出",
        sheetNames = {"门店调价"}, processor = PriceAdjustFormExportProcessor.class)
@Data
public class PriceAdjustFormExportResp {

    @ExcelProperty("调价单号")
    private String code;
    @ExcelProperty("定/调价维度")
    private String priceOpsType;
    @ExcelProperty("门店编码")
    private String storeCode;
    @ExcelProperty("申请人")
    private String createName;
    @ExcelProperty("商品编码")
    private String erpCode;
    @ExcelProperty("商品名称")
    private String erpName;
    @ExcelProperty("价格类型")
    private PriceTypeEnum requestPriceType;

    @ExcelProperty("门店价格")
    private BigDecimal retailPrice;
    @ExcelProperty("竞争对手名称")
    private String competitorName;
    @ExcelProperty("竞争对手售价")
    private BigDecimal competitorPrice;
    @ExcelProperty("是否有照片")
    private String photoFlag;
    @ExcelProperty("照片")
    private String competitorImg;

    @ExcelProperty("门店申请价格")
    private BigDecimal requestPrice;
    @ExcelProperty("申请调价开始日期")
    private LocalDateTime requestStartTime;
    @ExcelProperty("申请调价结束日期")
    private LocalDateTime requestEndTime;

    // 导出默认下拉框选择值 请选择/同意/不同意
    @ExcelProperty("审批结果")
    private String approvalResult;
    @ExcelProperty("批准售价")
    private BigDecimal approvePrice;
    @ExcelProperty("调价开始日期")
    private LocalDateTime approveStartTime;
    @ExcelProperty("调价结束日期")
    private LocalDateTime approveEndTime;
    @ExcelProperty("备注")
    private String descs;

}
