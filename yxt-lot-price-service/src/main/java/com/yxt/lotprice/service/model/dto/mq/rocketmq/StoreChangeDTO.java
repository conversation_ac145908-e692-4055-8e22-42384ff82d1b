package com.yxt.lotprice.service.model.dto.mq.rocketmq;

import com.yxt.lotprice.service.model.enums.MqStoreChangeTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * Since: 2025/05/28 15:03
 * Author: qs
 */

@Data
public class StoreChangeDTO {

    private String stCode;

    private String stId;

    private String operateType;

    private List<StoreChangeDetailDTO> data;

    @Data
    public static class StoreChangeDetailDTO {

        private String changeType;

        private String oldValue;

        private String newValue;
    }

}
