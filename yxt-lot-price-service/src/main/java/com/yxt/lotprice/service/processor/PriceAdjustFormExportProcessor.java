package com.yxt.lotprice.service.processor;


import com.yxt.batch.context.BatchAsyncExportTaskContext;
import com.yxt.batch.domain.BatchTask;
import com.yxt.batch.processor.HydeeAsyncBatchExportProcessor;
import com.yxt.lotprice.service.model.dto.b.response.PriceAdjustFormExportResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class PriceAdjustFormExportProcessor implements HydeeAsyncBatchExportProcessor<PriceAdjustFormExportResp> {


    @Override
    public List<PriceAdjustFormExportResp> process(BatchAsyncExportTaskContext taskContext) {
        return null;
    }

    @Override
    public Integer exportCount(BatchAsyncExportTaskContext taskContext) {
        return null;
    }

    @Override
    public void before(BatchTask batchTask) {

    }

    @Override
    public void after(BatchTask batchTask) {

    }

    @Override
    public void exceptionExe(BatchTask batchTask, Exception e) {

    }
}
